# OMOP Database Docker Automation - Implementation Plan

## 📋 Executive Summary

**Objective**: Create Docker containerization for the OMOP CDM database setup, building upon the completed automation scripts.

**Current State**: ✅ **Core automation scripts completed and validated**
**Target State**: Docker-based one-command setup taking 5-20 minutes
**Scope**: Docker integration only - core functionality already implemented

## ✅ **COMPLETED FOUNDATION**

### **Working Automation Scripts** (Ready for Docker Integration)

1. **Database Creation**: `servers/omop-database/scripts/setup/create_database.py`
   - Automates Steps 2-6 from postgresql_setup.md
   - Downloads official OHDSI DDL scripts (v5.4.2)
   - Creates 39/39 OMOP tables with proper structure

2. **Vocabulary Loading**: `servers/omop-database/scripts/setup/load_vocabularies.py`
   - Official OHDSI methodology (Drop constraints → Load data → Re-create constraints)
   - Performance: ~30K records/sec validated
   - Smart prerequisites checking with --force option

3. **Status Validation**: `servers/omop-database/scripts/setup/database_checker.py`
   - Complete database status checking
   - Table count verification (39/39 OMOP tables)
   - Vocabulary loading status detection

4. **Configuration Management**: `servers/omop-database/scripts/setup/config.py`
   - Environment-based configuration
   - Dynamic path resolution and validation
   - Logging setup with file and console handlers

## 🎯 **REMAINING WORK** (Docker Integration)

### **What Needs to be Done**

1. **Docker Infrastructure**:
   - Create `docker-compose.yml` following `servers/fhir-server/` pattern
   - Create `Dockerfile.setup` for automation scripts
   - Create `manage-omop-database.sh` management script

2. **Integration**:
   - Integrate existing scripts with Docker containers
   - Add health checks using existing database_checker.py
   - Configure environment variables for Docker

3. **Documentation**:
   - Create Docker setup guide
   - Update OMOP documentation with Docker option
   - Create README for servers/omop-database/

## 📚 **Key References**

### **Essential Files to Study**

1. **Docker Pattern Reference**:
   - `servers/fhir-server/manage-fhir-server.sh` - Management script pattern
   - `servers/fhir-server/docker-compose-postgres.yml` - Docker Compose pattern
   - `servers/fhir-server/README.md` - Documentation pattern

2. **Existing Automation Scripts** (to integrate):
   - `servers/omop-database/scripts/setup/create_database.py` - Database creation
   - `servers/omop-database/scripts/setup/load_vocabularies.py` - Vocabulary loading
   - `servers/omop-database/scripts/setup/database_checker.py` - Status validation
   - `servers/omop-database/scripts/setup/config.py` - Configuration management

3. **Project Standards**:
   - `docs/guides/development/standards.md` - Coding standards
   - `.env` - Environment configuration

## 🎯 **Implementation Tasks**

### **Task 1: Docker Infrastructure**

**Create Docker Files**:
```
servers/omop-database/
├── docker-compose.yml             # PostgreSQL + setup container
├── Dockerfile.setup               # Container for automation scripts
├── manage-omop-database.sh        # Management script (executable)
├── .env.example                   # Docker environment template
└── init-scripts/                  # PostgreSQL initialization
    └── 01-create-extensions.sql
```

**Management Script Commands**:
```bash
./manage-omop-database.sh start     # Start PostgreSQL
./manage-omop-database.sh setup     # Run create_database.py + load_vocabularies.py
./manage-omop-database.sh status    # Run database_checker.py
./manage-omop-database.sh stop      # Stop services
./manage-omop-database.sh reset     # Reset database
./manage-omop-database.sh logs      # Show logs
```

### **Task 2: Docker Integration**

**Docker Compose Configuration**:
- PostgreSQL 14 container (following fhir-server pattern)
- Setup container with existing Python scripts
- Volume mounts for vocabulary data and DDL scripts
- Health checks using existing database_checker.py

**Environment Configuration**:
```bash
# Docker-specific variables to add to .env
OMOP_POSTGRES_EXTERNAL_PORT=5434
OMOP_CONTAINER_NAME=omop-postgres
```

### **Task 3: Documentation**

**Files to Create/Update**:
- `servers/omop-database/README.md` - Complete Docker documentation
- `docs/guides/omop/database/docker_setup.md` - Quick start guide
- Update `docs/guides/omop/README.md` with Docker option

## 🔧 **Technical Requirements**

### **Docker Configuration**

**PostgreSQL Optimization** (following fhir-server pattern):
```yaml
environment:
  POSTGRES_SHARED_PRELOAD_LIBRARIES: pg_stat_statements
  POSTGRES_MAX_CONNECTIONS: 200
  POSTGRES_SHARED_BUFFERS: 256MB
  POSTGRES_EFFECTIVE_CACHE_SIZE: 1GB

volumes:
  - omop-postgres-data:/var/lib/postgresql/data
  - ../../data/vocabulary:/data/vocabulary:ro
  - ../../scripts/ddl:/scripts/ddl:ro
```

**Setup Container**:
```dockerfile
FROM python:3.11-slim
RUN apt-get update && apt-get install -y postgresql-client
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY scripts/ /app/scripts/
WORKDIR /app
```

### **Integration Points**

**Management Script Integration**:
```bash
case "$1" in
    setup)
        echo "Creating OMOP database..."
        docker exec setup-container python scripts/setup/create_database.py
        echo "Loading vocabularies..."
        docker exec setup-container python scripts/setup/load_vocabularies.py
        ;;
    status)
        docker exec setup-container python scripts/setup/database_checker.py
        ;;
esac
```

## 📊 **Success Criteria**

### **Functional Requirements**
1. **One-Command Setup**: `./manage-omop-database.sh setup` creates complete OMOP database
2. **Performance**: Maintains 30K+ records/sec vocabulary loading
3. **Reliability**: Uses existing validated scripts
4. **Integration**: Follows `servers/fhir-server/` Docker patterns

### **Deliverables**
- [x] ✅ **COMPLETED**: Core automation scripts
- [ ] 🔄 **REMAINING**: Docker infrastructure (`docker-compose.yml`, `Dockerfile.setup`)
- [ ] 🔄 **REMAINING**: Management script (`manage-omop-database.sh`)
- [ ] 🔄 **REMAINING**: Documentation (`README.md`, setup guides)

## ⚠️ **Critical Guidelines**

### **DO NOT RECREATE**
- ✅ Database creation logic (create_database.py works perfectly)
- ✅ Vocabulary loading methodology (load_vocabularies.py validated)
- ✅ Configuration management (config.py handles all environments)
- ✅ Error handling and validation (comprehensive and tested)

### **FOCUS ON**
- 🔄 Docker Compose configuration
- 🔄 Management script creation
- 🔄 Container orchestration
- 🔄 Documentation updates

---

## 🚀 **QUICK START FOR NEXT DEVELOPER**

### 📋 **What's Already Done**
- ✅ All core automation scripts implemented and validated
- ✅ Database creation: `create_database.py` (automates Steps 2-6)
- ✅ Vocabulary loading: `load_vocabularies.py` (30K+ records/sec)
- ✅ Status validation: `database_checker.py` (comprehensive checks)
- ✅ Configuration: `config.py` (environment-based, no hardcoded paths)

### 🎯 **Your Task: Docker Integration Only**

**Step 1** - Study the pattern (15 min):
```bash
# Review successful Docker implementation
ls -la servers/fhir-server/
cat servers/fhir-server/manage-fhir-server.sh
cat servers/fhir-server/docker-compose-postgres.yml
```

**Step 2** - Test existing automation (5 min):
```bash
# Verify scripts work
python servers/omop-database/scripts/setup/database_checker.py
python servers/omop-database/scripts/setup/create_database.py --help
```

**Step 3** - Create Docker files (60 min):
```bash
cd servers/omop-database/
# Create: docker-compose.yml, Dockerfile.setup, manage-omop-database.sh
# Pattern: Use existing scripts, don't recreate functionality
```

**Step 4** - Integration pattern:
```bash
# In manage-omop-database.sh
case "$1" in
    setup)
        docker exec setup-container python scripts/setup/create_database.py
        docker exec setup-container python scripts/setup/load_vocabularies.py
        ;;
    status)
        docker exec setup-container python scripts/setup/database_checker.py
        ;;
esac
```

### ⚠️ **Critical: Don't Recreate Working Code**
The automation scripts are complete and validated. Your job is Docker integration only - adding a convenience layer around proven functionality.

**Success = One command setup using existing scripts + Docker convenience**
