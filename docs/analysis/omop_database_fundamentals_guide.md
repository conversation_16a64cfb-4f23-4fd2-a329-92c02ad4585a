# OMOP CDM Database - Guía de Fundamentos
## Tu Primera Interacción: De Conceptos Básicos a Dominio Completo

**Fecha:** 3 de Julio, 2025  
**Tu Base de Datos:** 33.7 millones de registros cargados y operativos  
**Objetivo:** Comprender desde los fundamentos hasta el dominio completo  

---

## 🎯 **ÍNDICE DE APRENDIZAJE INCREMENTAL**

### **🏁 NIVEL 1: Los Fundamentos** (Empezar aquí)
1. [¿Qué es OMOP CDM?](#que-es-omop-cdm)
2. [La Arquitectura Básica](#arquitectura-basica)
3. [El Concepto Central: "Concept"](#concepto-central)

### **🚀 NIVEL 2: La Estructura**
4. [Los Vocabularios Médicos](#vocabularios-medicos)
5. [Cómo se Relacionan los Datos](#como-se-relacionan)
6. [Dominios Médicos](#dominios-medicos)

### **🎓 NIVEL 3: El Sistema Completo**
7. [Jerarquías y Navegación](#jerarquias-navegacion)
8. [Mapeo y Estandarización](#mapeo-estandarizacion)
9. [Casos de Uso Reales](#casos-uso-reales)

### **🏆 NIVEL 4: Dominio Experto**
10. [Análisis Avanzados](#analisis-avanzados)
11. [Troubleshooting](#troubleshooting)
12. [Próximos Pasos](#proximos-pasos)

---

## 🏁 **NIVEL 1: LOS FUNDAMENTOS**

### ¿Qué es OMOP CDM? {#que-es-omop-cdm}

**OMOP CDM** = **O**bservational **M**edical **O**utcomes **P**artnership **C**ommon **D**ata **M**odel

**En términos simples:**
- 🏥 **Una forma estándar** de organizar datos médicos
- 🌍 **Lenguaje universal** para que hospitales, investigadores y farmacias "hablen igual"
- 📊 **Base de datos diseñada** para análisis de salud a gran escala

**Analogía práctica:**
Imagina que cada hospital tiene su propio idioma para describir enfermedades:
- Hospital A: "Infarto"
- Hospital B: "Ataque cardíaco"  
- Hospital C: "Cardiopatía isquémica"

OMOP CDM es como un **traductor universal** que convierte todo esto a un código único: `concept_id = 4329847` para "Myocardial infarction"

### La Arquitectura Básica {#arquitectura-basica}

Tu base de datos tiene **3 tipos de tablas**:

#### **1. 🔥 TABLAS DE VOCABULARIO** (Las que están llenas)
```
concept           → 3.28M registros → El "diccionario médico universal"
concept_relationship → 15.83M registros → Cómo se relacionan los conceptos
concept_ancestor  → 11.76M registros → Jerarquías médicas
```

#### **2. ❌ TABLAS DE DATOS CLÍNICOS** (Las que están vacías)
```
person            → 0 registros → Pacientes
condition_occurrence → 0 registros → Diagnósticos
drug_exposure     → 0 registros → Medicamentos prescritos
```

#### **3. 🟡 TABLAS DE CONFIGURACIÓN** (Las pequeñas pero importantes)
```
vocabulary        → 48 registros → Los "diccionarios" disponibles
domain           → 50 registros → Categorías médicas
```

**¿Por qué esta estructura?**
- **Vocabularios** = El "cerebro" que entiende terminología médica
- **Datos clínicos** = Los datos reales de pacientes (que cargarás después)
- **Configuración** = Las "reglas del juego"

### El Concepto Central: "Concept" {#concepto-central}

**La tabla `concept` es el corazón de todo.**

Cada registro en `concept` representa **un término médico estandarizado**:

```sql
-- Ejemplo de un concept
SELECT concept_id, concept_name, vocabulary_id, domain_id 
FROM concept 
WHERE concept_name = 'Myocardial infarction';

-- Resultado:
-- concept_id: 4329847
-- concept_name: Myocardial infarction  
-- vocabulary_id: SNOMED
-- domain_id: Condition
```

**¿Qué significa esto?**
- `concept_id = 4329847` = El ID único global para "infarto"
- `concept_name = 'Myocardial infarction'` = El nombre estándar
- `vocabulary_id = 'SNOMED'` = Viene del vocabulario SNOMED CT
- `domain_id = 'Condition'` = Es una condición médica

**Tus 3.28 millones de concepts incluyen:**
- 💊 1.82M medicamentos
- 🏥 266.4K condiciones/enfermedades  
- 🧪 191.6K mediciones de laboratorio
- ⚕️ 108.2K procedimientos médicos

---

## 🚀 **NIVEL 2: LA ESTRUCTURA**

### Los Vocabularios Médicos {#vocabularios-medicos}

**¿Qué son los vocabularios?**
Son "diccionarios médicos" oficiales creados por organizaciones de salud.

**Tus 48 vocabularios principales:**

#### **🩺 SNOMED CT (1.09M conceptos)**
- **Qué es:** Terminología clínica internacional
- **Creado por:** IHTSDO (International Health Terminology Standards Development Organisation)
- **Ejemplo:** "Diabetes mellitus type 2" = concept_id 44054006
- **Uso:** Diagnósticos, procedimientos, anatomía

#### **💊 NDC (1.25M conceptos)**
- **Qué es:** National Drug Code (FDA)
- **Creado por:** FDA (Food and Drug Administration)
- **Ejemplo:** "Metformin 500mg Tablet" = concept_id específico
- **Uso:** Prescripciones, farmacias, seguimiento de medicamentos

#### **🧪 LOINC (274.9K conceptos)**
- **Qué es:** Logical Observation Identifiers Names and Codes
- **Creado por:** Regenstrief Institute
- **Ejemplo:** "Hemoglobin A1c" = concept_id 4184637
- **Uso:** Laboratorios, análisis de sangre, mediciones

#### **📋 ICD-10-CM (99.4K conceptos)**
- **Qué es:** International Classification of Diseases
- **Creado por:** WHO + CMS
- **Ejemplo:** "E11.9 - Type 2 diabetes" = concept_id específico
- **Uso:** Facturación, epidemiología, reportes oficiales

### Cómo se Relacionan los Datos {#como-se-relacionan}

**La tabla `concept_relationship` (15.83M registros) conecta todo:**

```sql
-- Ejemplo: ¿Qué medicamentos tratan la diabetes?
SELECT c2.concept_name as medication
FROM concept_relationship cr
JOIN concept c1 ON cr.concept_id_1 = c1.concept_id
JOIN concept c2 ON cr.concept_id_2 = c2.concept_id
WHERE c1.concept_name = 'Diabetes mellitus type 2'
  AND cr.relationship_id = 'Has_treatment';
```

**Tipos de relaciones principales:**
- **"Maps to"** (4.40M) = Conversión automática de códigos
- **"Is a"** (3.33M) = Jerarquías (ej: "Diabetes tipo 2" → "Diabetes")
- **"Subsumes"** = Categorías padre-hijo
- **"Has_treatment"** = Medicamentos para condiciones

### Dominios Médicos {#dominios-medicos}

**Los 5 dominios principales organizan la medicina:**

#### **💊 Drug (1.82M conceptos - 55.4%)**
```sql
-- Todos los medicamentos para diabetes
SELECT concept_name FROM concept 
WHERE domain_id = 'Drug' 
  AND concept_name ILIKE '%diabetes%';
```

#### **🏥 Condition (266.4K conceptos - 8.1%)**
```sql
-- Todas las enfermedades cardiovasculares
SELECT concept_name FROM concept 
WHERE domain_id = 'Condition' 
  AND concept_name ILIKE '%heart%';
```

#### **🧪 Measurement (191.6K conceptos - 5.8%)**
```sql
-- Todos los análisis de laboratorio
SELECT concept_name FROM concept 
WHERE domain_id = 'Measurement' 
  AND concept_name ILIKE '%blood%';
```

#### **⚕️ Procedure (108.2K conceptos - 3.3%)**
```sql
-- Todos los procedimientos quirúrgicos
SELECT concept_name FROM concept 
WHERE domain_id = 'Procedure' 
  AND concept_name ILIKE '%surgery%';
```

#### **📊 Observation (374.2K conceptos - 11.4%)**
```sql
-- Observaciones clínicas y sociales
SELECT concept_name FROM concept 
WHERE domain_id = 'Observation' 
  AND concept_name ILIKE '%smoking%';
```

---

## 🎓 **NIVEL 3: EL SISTEMA COMPLETO**

### Jerarquías y Navegación {#jerarquias-navegacion}

**La tabla `concept_ancestor` (11.76M registros) crea jerarquías:**

```sql
-- Ejemplo: Todos los tipos de diabetes
SELECT 
    c.concept_name,
    ca.min_levels_of_separation as niveles
FROM concept_ancestor ca
JOIN concept c ON ca.descendant_concept_id = c.concept_id
WHERE ca.ancestor_concept_id = (
    SELECT concept_id FROM concept 
    WHERE concept_name = 'Diabetes mellitus'
)
ORDER BY ca.min_levels_of_separation;
```

**Resultado esperado:**
```
Diabetes mellitus                    → Nivel 0 (raíz)
├── Diabetes mellitus type 1         → Nivel 1
├── Diabetes mellitus type 2         → Nivel 1
│   ├── Diabetes with nephropathy    → Nivel 2
│   └── Diabetes with retinopathy    → Nivel 2
└── Gestational diabetes             → Nivel 1
```

### Mapeo y Estandarización {#mapeo-estandarizacion}

**El problema que OMOP resuelve:**
- Hospital A usa código ICD-10: "E11.9"
- Hospital B usa código local: "DM2"  
- Hospital C usa SNOMED: "44054006"

**Todos significan:** "Diabetes mellitus type 2"

**La solución OMOP:**
```sql
-- Convertir cualquier código a estándar
SELECT 
    source_code,
    source_vocabulary_id,
    target_concept_id,
    target_concept_name
FROM source_to_concept_map
WHERE source_code IN ('E11.9', 'DM2', '44054006');
```

**Resultado:**
```
E11.9    → ICD-10-CM → 201826 → Diabetes mellitus type 2
DM2      → Local    → 201826 → Diabetes mellitus type 2  
44054006 → SNOMED   → 201826 → Diabetes mellitus type 2
```

### Casos de Uso Reales {#casos-uso-reales}

#### **🔍 Caso 1: Búsqueda Inteligente**
```sql
-- Encuentra todos los conceptos relacionados con "corazón"
SELECT DISTINCT c.concept_name, c.vocabulary_id, c.domain_id
FROM concept c
LEFT JOIN concept_synonym cs ON c.concept_id = cs.concept_id
WHERE c.concept_name ILIKE '%heart%' 
   OR cs.concept_synonym_name ILIKE '%heart%'
   OR c.concept_name ILIKE '%cardiac%'
   OR c.concept_name ILIKE '%cardio%'
ORDER BY c.domain_id, c.concept_name;
```

#### **🧬 Caso 2: Análisis Farmacológico**
```sql
-- Medicamentos con sus dosis para hipertensión
SELECT 
    c.concept_name as medication,
    ds.amount_value,
    ds.amount_unit_concept_id,
    u.concept_name as unit
FROM concept c
JOIN drug_strength ds ON c.concept_id = ds.drug_concept_id
JOIN concept u ON ds.amount_unit_concept_id = u.concept_id
WHERE c.concept_name ILIKE '%amlodipine%';
```

#### **🏥 Caso 3: Análisis Epidemiológico**
```sql
-- Jerarquía completa de enfermedades cardiovasculares
WITH cardiovascular_tree AS (
    SELECT 
        ancestor_concept_id,
        descendant_concept_id,
        min_levels_of_separation
    FROM concept_ancestor
    WHERE ancestor_concept_id = (
        SELECT concept_id FROM concept 
        WHERE concept_name = 'Cardiovascular disease'
    )
)
SELECT 
    c.concept_name,
    c.vocabulary_id,
    ct.min_levels_of_separation as nivel
FROM cardiovascular_tree ct
JOIN concept c ON ct.descendant_concept_id = c.concept_id
ORDER BY ct.min_levels_of_separation, c.concept_name;
```

---

## 🏆 **NIVEL 4: DOMINIO EXPERTO**

### Análisis Avanzados {#analisis-avanzados}

#### **📊 Analytics de Vocabularios**
```sql
-- Análisis de cobertura por vocabulario
SELECT 
    v.vocabulary_name,
    COUNT(*) as total_concepts,
    COUNT(CASE WHEN c.standard_concept = 'S' THEN 1 END) as standard_concepts,
    ROUND(
        COUNT(CASE WHEN c.standard_concept = 'S' THEN 1 END) * 100.0 / COUNT(*), 
        2
    ) as percent_standard
FROM concept c
JOIN vocabulary v ON c.vocabulary_id = v.vocabulary_id
GROUP BY v.vocabulary_name
ORDER BY total_concepts DESC;
```

#### **🔗 Análisis de Relaciones**
```sql
-- Densidad de relaciones por dominio
SELECT 
    d.domain_name,
    COUNT(DISTINCT c.concept_id) as concepts,
    COUNT(cr.concept_id_1) as total_relationships,
    ROUND(
        COUNT(cr.concept_id_1) * 1.0 / COUNT(DISTINCT c.concept_id), 
        2
    ) as avg_relationships_per_concept
FROM concept c
JOIN domain d ON c.domain_id = d.domain_id
LEFT JOIN concept_relationship cr ON c.concept_id = cr.concept_id_1
GROUP BY d.domain_name
ORDER BY avg_relationships_per_concept DESC;
```

### Troubleshooting {#troubleshooting}

#### **🔍 Problemas Comunes y Soluciones**

**Problema 1: "No encuentro un concepto"**
```sql
-- Búsqueda amplia con sinónimos
SELECT 
    c.concept_id,
    c.concept_name,
    c.vocabulary_id,
    'Main name' as source
FROM concept c
WHERE c.concept_name ILIKE '%tu_termino%'

UNION ALL

SELECT 
    c.concept_id,
    c.concept_name,
    c.vocabulary_id,
    'Synonym' as source
FROM concept c
JOIN concept_synonym cs ON c.concept_id = cs.concept_id
WHERE cs.concept_synonym_name ILIKE '%tu_termino%';
```

**Problema 2: "¿Qué código estándar uso?"**
```sql
-- Encontrar el concepto estándar para un término
SELECT 
    c.concept_id,
    c.concept_name,
    c.standard_concept,
    c.vocabulary_id
FROM concept c
WHERE c.concept_name ILIKE '%tu_termino%'
  AND c.standard_concept = 'S'  -- Solo conceptos estándar
  AND c.invalid_reason IS NULL;  -- Solo conceptos válidos
```

**Problema 3: "¿Cómo convertir códigos locales?"**
```sql
-- Mapear códigos locales a estándar
SELECT 
    scm.source_code,
    scm.source_vocabulary_id,
    c.concept_id as target_concept_id,
    c.concept_name as target_concept_name
FROM source_to_concept_map scm
JOIN concept c ON scm.target_concept_id = c.concept_id
WHERE scm.source_code = 'tu_codigo_local';
```

### Próximos Pasos {#proximos-pasos}

#### **🎯 Fase Actual: Vocabularios Cargados ✅**
Tu base de datos está en el estado perfecto para comenzar análisis.

#### **🚀 Siguiente Nivel: Cargar Datos Clínicos**
```bash
# Cuando estés listo, podrás cargar datos FHIR
python servers/omop-database/scripts/etl/load_fhir_data.py
```

#### **📊 Futuro: Analytics Avanzados**
- Dashboards interactivos
- Machine learning sobre datos médicos
- Reportes regulatorios
- Investigación clínica

---

## 🎉 **RESUMEN DE TU DOMINIO ALCANZADO**

### **✅ Ahora Entiendes:**
1. **Qué es OMOP CDM** y por qué es revolucionario
2. **La arquitectura** de vocabularios vs datos clínicos
3. **Los 48 vocabularios** y su propósito específico
4. **Los 5 dominios médicos** y cómo navegar entre ellos
5. **Las jerarquías** y cómo hacer roll-up de datos
6. **El mapeo automático** de códigos locales a estándar
7. **Queries complejas** para análisis reales

### **💡 Tu Ventaja Competitiva:**
- **Base de conocimiento**: 3.28M conceptos médicos
- **Interoperabilidad**: 48 vocabularios estándar
- **Escalabilidad**: Arquitectura probada globalmente
- **Análisis ready**: Queries optimizadas disponibles

### **🔥 Casos de Uso Inmediatos:**
- Búsqueda inteligente de terminología médica
- Análisis farmacológico con dosificaciones
- Mapeo de códigos locales a estándar internacional
- Navegación jerárquica de condiciones médicas
- Análisis de cobertura de vocabularios

---

## 📚 **RECURSOS ADICIONALES**

### **📖 Documentación Complementaria:**
- `docs/guides/omop/` - Guías técnicas detalladas
- `docs/tutorials/` - Tutoriales paso a paso
- `docs/mappings/` - Mapeos específicos por vocabulario

### **🔧 Scripts de Referencia:**
- `create_database.py` - Cómo se creó tu base de datos
- `load_vocabularies.py` - Cómo se cargaron los vocabularios
- `database_checker.py` - Verificaciones de integridad

### **🌐 Comunidad:**
- [OHDSI Forums](https://forums.ohdsi.org/) - Comunidad global
- [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/) - Documentación oficial

---

**¡Felicitaciones!** 🎉 Has pasado de principiante a tener un dominio sólido de OMOP CDM. Tu base de datos con 33.7 millones de registros está lista para cualquier análisis de salud que imagines.

**¿Siguiente paso?** Pregúntame sobre cualquier concepto específico o comenzemos a explorar casos de uso prácticos con tus datos.

*Documento creado el 3 de Julio, 2025*  
*Tu base de datos OMOP CDM: Completamente operativa y lista para análisis*
