# OMOP CDM Database Structure Overview
## Data Science Team Presentation

**Date:** July 3, 2025  
**Database:** omop_cdm (PostgreSQL 14.18)  
**Status:** Newly Created - Ready for Vocabulary Loading  

---

## 📊 Executive Summary

This document provides a comprehensive overview of our newly created OMOP CDM (Observational Medical Outcomes Partnership Common Data Model) database structure, designed for healthcare data standardization and analytics. The database is currently in its initial state with complete schema structure but awaiting vocabulary and clinical data loading.

### Key Highlights
- **Database Type:** PostgreSQL 14.18 on macOS (ARM64)
- **Data Model:** OMOP CDM v5.4 (Industry Standard)
- **Current Status:** Schema Complete, Data Loading Pending
- **Total Tables:** 41 tables across 6 functional domains
- **Primary Purpose:** Healthcare data standardization and analytics

---

## 🏗️ Database Architecture Overview

### OMOP CDM Design Philosophy

The OMOP CDM is designed as a **patient-centric** healthcare data warehouse that:
- **Standardizes** diverse healthcare data sources
- **Enables** cross-institutional research and analytics
- **Supports** longitudinal patient analysis
- **Facilitates** interoperability between systems

### Core Design Principles

1. **Person-Centric Model**: All clinical events link to individual patients
2. **Standardized Vocabularies**: Common terminologies across all concepts
3. **Temporal Tracking**: Complete timeline of patient healthcare journey
4. **Derived Analytics**: Pre-computed clinical indicators and eras

---

## 📋 Table Structure Analysis

### 1. **Clinical Data Tables** (Primary Domain)
*Core patient clinical information and healthcare events*

| Table | Purpose | Key Relationships | Analytics Use Case |
|-------|---------|-------------------|-------------------|
| `person` | Patient demographics and basic info | → All clinical tables | Population analysis, cohort definition |
| `observation_period` | Time periods when patient was observable | person → | Data quality, follow-up analysis |
| `visit_occurrence` | Healthcare encounters (visits) | person → | Care patterns, utilization analysis |
| `condition_occurrence` | Diagnoses and medical conditions | person, visit → | Disease prevalence, comorbidity analysis |
| `drug_exposure` | Medication prescriptions and administrations | person, visit → | Pharmacovigilance, treatment patterns |
| `procedure_occurrence` | Medical procedures and interventions | person, visit → | Clinical pathway analysis |
| `measurement` | Lab results, vital signs, test results | person, visit → | Clinical indicators, biomarker analysis |
| `observation` | Clinical facts not covered by other domains | person, visit → | Social determinants, lifestyle factors |

**Analytics Value**: These tables form the foundation for clinical research, population health studies, and patient outcome analysis.

### 2. **Vocabulary/Standardization Tables** (Critical Foundation)
*Terminologies and coding systems that standardize all clinical concepts*

| Table | Purpose | Scale | Analytics Impact |
|-------|---------|-------|------------------|
| `concept` | Universal concept dictionary | ~3.3M records | Core standardization layer |
| `vocabulary` | Vocabulary metadata (SNOMED, ICD10, etc.) | ~50 records | Terminology management |
| `domain` | Concept domains (Drug, Condition, etc.) | ~50 records | Data categorization |
| `concept_class` | Concept classifications | ~400 records | Hierarchical organization |
| `concept_relationship` | Relationships between concepts | ~15.8M records | Semantic networks, mapping |
| `concept_synonym` | Alternative names for concepts | ~2.6M records | Text mining, NLP support |
| `concept_ancestor` | Hierarchical concept relationships | ~11.7M records | Roll-up analysis, classifications |
| `drug_strength` | Pharmaceutical strength information | ~200K records | Dose calculations, equivalencies |

**Analytics Value**: These tables enable data harmonization across different source systems and support advanced analytics through standardized terminologies.

### 3. **Healthcare Infrastructure Tables** (Contextual Data)
*Information about healthcare settings and providers*

| Table | Purpose | Analytics Use Case |
|-------|---------|-------------------|
| `location` | Geographic information | Geographic analysis, health disparities |
| `care_site` | Healthcare facilities | Network analysis, care access |
| `provider` | Healthcare professionals | Provider performance, specialty analysis |

### 4. **Financial/Insurance Tables** (Cost Analysis)
*Healthcare economics and payer information*

| Table | Purpose | Analytics Use Case |
|-------|---------|-------------------|
| `cost` | Healthcare costs and financial data | Economic analysis, cost-effectiveness |
| `payer_plan_period` | Insurance coverage periods | Coverage analysis, access patterns |

### 5. **Derived Data Tables** (Advanced Analytics)
*Pre-computed clinical indicators and treatment episodes*

| Table | Purpose | Analytics Value |
|-------|---------|-----------------|
| `drug_era` | Continuous medication periods | Treatment adherence, duration analysis |
| `condition_era` | Disease episode periods | Disease progression, recurrence analysis |
| `dose_era` | Dose-specific medication periods | Dosing pattern analysis |

### 6. **Metadata and Special Purpose Tables**
*System metadata and specialized analytical constructs*

| Table | Purpose | Use Case |
|-------|---------|----------|
| `cdm_source` | Database metadata | Data provenance, version tracking |
| `cohort` | Analytical cohorts | Clinical studies, population subsets |
| `metadata` | System metadata | Data quality, ETL tracking |
| `note` | Clinical notes | NLP analysis, unstructured data |
| `episode` | Clinical episodes | Care episodes, treatment sequences |

---

## 🔗 Relationship Architecture

### Primary Relationships

```
PERSON (Central Hub)
├── observation_period (1:Many)
├── visit_occurrence (1:Many)
├── condition_occurrence (1:Many)
├── drug_exposure (1:Many)
├── procedure_occurrence (1:Many)
├── measurement (1:Many)
└── observation (1:Many)

CONCEPT (Standardization Hub)
├── All clinical tables (via *_concept_id)
├── concept_relationship (Many:Many)
├── concept_ancestor (Hierarchical)
└── concept_synonym (1:Many)
```

### Key Foreign Key Patterns

1. **Person-centric**: All clinical events reference `person.person_id`
2. **Visit-linked**: Most clinical events reference `visit_occurrence.visit_occurrence_id`
3. **Concept-standardized**: All clinical concepts reference `concept.concept_id`
4. **Vocabulary-supported**: All standardized terms use vocabulary tables

---

## 📈 Data Population Strategy

### Current State: Schema Complete ✅
- All 41 tables created with proper schema
- Foreign key constraints established
- Indexes created for performance
- User permissions configured

### Next Steps: Data Loading 🔄

#### Phase 1: Vocabulary Loading (Critical Foundation)
**Priority:** Immediate
**Expected Data Volume:** ~33 million records
**Key Vocabularies:**
- **SNOMED CT** (~1.1M concepts) - Clinical terminologies
- **ICD-10-CM** (~99K concepts) - Diagnosis codes
- **LOINC** (~275K concepts) - Lab tests and observations
- **RxNorm** (~311K concepts) - Medication names
- **CPT-4** (~18K concepts) - Procedure codes
- **NDC** (~1.2M concepts) - Drug identifiers

#### Phase 2: Clinical Data Loading (ETL Pipeline)
**Priority:** Post-vocabulary
**Data Sources:** FHIR resources, EHR systems, claims data
**Expected Volume:** Variable based on data sources

---

## 🎯 Analytics Capabilities

### Enabled Analytics (Post-Data Loading)

#### 1. **Population Health Analytics**
- Disease prevalence and incidence
- Comorbidity analysis
- Geographic health patterns
- Demographic health disparities

#### 2. **Clinical Research**
- Cohort identification and analysis
- Treatment effectiveness studies
- Adverse event detection
- Clinical pathway analysis

#### 3. **Pharmacovigilance**
- Drug utilization studies
- Adverse drug reaction monitoring
- Medication adherence analysis
- Drug-drug interaction studies

#### 4. **Healthcare Operations**
- Care quality metrics
- Provider performance analysis
- Resource utilization patterns
- Cost-effectiveness analysis

#### 5. **Longitudinal Patient Analysis**
- Patient journey mapping
- Disease progression modeling
- Treatment response analysis
- Outcome prediction

---

## 🔍 Technical Specifications

### Database Performance Features

#### Indexing Strategy
- **Primary Keys**: All tables have optimized primary keys
- **Foreign Keys**: Indexed for join performance
- **Clinical Queries**: Specialized indexes for common query patterns
- **Concept Lookups**: Optimized vocabulary table indexes

#### Key Performance Indexes
```sql
-- Patient-centric queries
idx_person_id, idx_gender
idx_observation_period_id_1 (person_id)
idx_visit_person_id_1, idx_condition_person_id_1
idx_drug_person_id_1, idx_procedure_person_id_1

-- Concept-based queries
idx_concept_concept_id, idx_concept_code
idx_concept_vocabulary_id, idx_concept_domain_id
idx_concept_relationship_id_1, idx_concept_ancestor_id_1

-- Temporal queries
idx_visit_concept_id_1, idx_condition_concept_id_1
```

### Data Integrity Features

#### Constraint Management
- **Foreign Key Constraints**: Ensure referential integrity
- **Primary Key Constraints**: Ensure unique identifiers
- **Check Constraints**: Validate data quality rules
- **Not Null Constraints**: Ensure required fields

#### Data Quality Validation
- **Vocabulary Validation**: All concepts must exist in vocabulary
- **Date Validation**: Temporal consistency checks
- **Value Validation**: Range and format checks

---

## 🚀 Data Science Team Readiness

### Ready for Use ✅
- **Database Schema**: Complete and optimized
- **Security**: Proper user access controls
- **Performance**: Indexed for analytical queries
- **Documentation**: Comprehensive structure documentation

### Pending for Full Analytics 🔄
- **Vocabulary Loading**: Required for concept standardization
- **Clinical Data Loading**: Required for actual analytics
- **Data Quality Validation**: Post-load validation processes

### Analytical Tools Integration
- **SQL Analytics**: Direct PostgreSQL connectivity
- **Python/R**: Database connectors available
- **BI Tools**: Standard database connection protocols
- **Jupyter Notebooks**: Ready for data science workflows

---

## 📊 Expected Data Volumes (Post-Loading)

### Vocabulary Tables (Foundation)
| Table | Expected Records | Size Estimate |
|-------|-----------------|---------------|
| `concept` | ~3.3M | ~500 MB |
| `concept_relationship` | ~15.8M | ~2.5 GB |
| `concept_ancestor` | ~11.7M | ~1.8 GB |
| `concept_synonym` | ~2.6M | ~400 MB |
| **Total Vocabulary** | **~33M** | **~5 GB** |

### Clinical Data Tables (Variable)
*Depends on source data volume*
- **Small Dataset**: 1K-10K patients
- **Medium Dataset**: 10K-100K patients  
- **Large Dataset**: 100K+ patients

### Performance Characteristics
- **Query Response**: Sub-second for indexed queries
- **Analytical Queries**: Optimized for complex joins
- **Concurrent Users**: Designed for multi-user analytics
- **Data Loading**: Bulk loading optimized (~62K records/sec)

---

## 🎯 Immediate Next Steps

### 1. Vocabulary Loading (Priority 1)
**Action Required:** Execute vocabulary loading process
**Expected Duration:** 7-15 minutes
**Data Volume:** ~33 million records
**Script:** `scripts/load_vocabularies.py`

### 2. Data Quality Validation (Priority 2)
**Action Required:** Validate vocabulary loading
**Expected Duration:** 5-10 minutes
**Validation Points:** Record counts, vocabulary coverage

### 3. Clinical Data Integration Planning (Priority 3)
**Action Required:** Plan FHIR → OMOP ETL pipeline
**Expected Duration:** Planning phase
**Dependencies:** Vocabulary loading complete

---

## 📚 Resources and Documentation

### Technical Documentation
- **OMOP CDM Specification**: [Official OHDSI Documentation](https://ohdsi.github.io/CommonDataModel/)
- **Vocabulary Documentation**: [OMOP Vocabularies](https://ohdsi.github.io/CommonDataModel/vocabulary.html)
- **Database Setup Guide**: `docs/guides/omop/vocabulary/loading.md`

### Training Resources
- **OMOP CDM Tutorial**: `docs/tutorials/02_Introduction_to_OMOP_CDM.ipynb`
- **Vocabulary Tutorial**: `docs/tutorials/vocabulary_download.md`
- **Learning Path**: `docs/tutorials/learning_path.md`

### Community Resources
- **OHDSI Community**: [https://ohdsi.org/](https://ohdsi.org/)
- **OHDSI Forums**: [https://forums.ohdsi.org/](https://forums.ohdsi.org/)
- **GitHub Repository**: [https://github.com/OHDSI/CommonDataModel](https://github.com/OHDSI/CommonDataModel)

---

## 🔮 Future Enhancements

### Planned Features
1. **Real-time Data Integration**: Streaming data pipelines
2. **Advanced Analytics**: Machine learning model integration
3. **Visualization Dashboards**: Interactive analytics dashboards
4. **API Development**: RESTful API for data access
5. **Data Quality Monitoring**: Automated data quality checks

### Scalability Considerations
- **Horizontal Scaling**: PostgreSQL clustering options
- **Performance Optimization**: Query optimization and caching
- **Archive Strategy**: Historical data management
- **Backup/Recovery**: Automated backup strategies

---

*This document represents the current state of the OMOP CDM database as of July 3, 2025. The database is ready for vocabulary loading and subsequent clinical data integration for full analytical capabilities.*
